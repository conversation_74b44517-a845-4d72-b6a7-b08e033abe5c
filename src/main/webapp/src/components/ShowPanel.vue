<!-- components/Shared/ShowPanel.vue -->
<template class="custom-drawer-width">
  <q-drawer
    side="right"
    width="25vw"
    show-if-above
    :model-value="open"
    behavior="desktop"
    overlay
  >
    <div class="bg-backgroundSecondary">
      <div class="show-panel-header">
        <div class="flex justify-end">
          <q-btn
            class="buttonNeutral"
            icon="close"
            @click="$emit('close')"
          />
        </div>
        <div class="bg-backgroundSecondary ">
          <div>
            <div class="show-panel-label bg-infoLowest text-infoMedium">
              <q-icon :name="icon" />
              <p>{{ label }}</p>
            </div>
          </div>
        </div>
      </div>
      <slot name="default" />
      <q-separator />
      <slot name="subsection" />
    </div>
  </q-drawer>
</template>

<script setup>
defineProps({
  open: Boolean,
  label: String,
  icon: String
});
</script>
<style lang="scss">
.custom-drawer-width {
  width: 25vw !important;
}

.show-panel-header {
  padding: 12px 16px 24px;
  gap: 8px;
  display: grid;
}

.show-panel-label {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px;
  border-radius: 4px;


  p {
    margin: 0;
    font-weight: bolder;
  }

}


</style>
