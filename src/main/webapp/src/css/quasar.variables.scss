// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

// Font
//@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

// Backgrounds
$surface: #ffffff;
$container: #f6f8fa;
$backgroundSecondary: #f1f1f1;
$backgroundTertiary: #e6ebe9;

// Borders
$neutral: #d5dbe1;
$borderPrimary: $backgroundSecondary;
$borderSecondary: #dde4e2;

// Text & icons
$primary: #232348;
$secondary: #a1a0bd;
$disabled: #a3acba;
$accent: #625afa;
$brandBlue: #232348;
$brandYellow: #fcd983;
$brandMedium: #33a68d;
$brandLower: #a9e6d3;
$positive: #28cc42;
$negative: #ff3b2f;
$neutralLowest: #fcfcfc;
$neutralHigh: #737876;
$neutralHigher: #434645;
$neutralHighest: #0a0909;
$infoMedium: #047aff;
$infoLowest: #D7EFFF;
$warning: #ff9500;

// Dark mode
$dark-page: #1d1d1d;
$backgroundSecondaryDark: #161716;
$borderSecondaryDark: #242626;
$neutralHigherDark: #c1c8c6;
$neutralHighestDark: #fcfcfc;
$neutralLowestDark: #0a0909;
$infoLowestDark: #112d5a;

//Typography Font-family
$typography-font-family:
  Noto Sans,
  sans-serif;

// Custom quasar classes
.text-brandMedium {
  color: $brandMedium !important;
}

.bg-brandMedium {
  background: $brandMedium !important;
}

.text-brandLower {
  color: $brandLower !important;
}

.bg-brandLower {
  background: $brandLower !important;
}

.text-borderSecondary {
  color: $borderSecondary !important;
}

.bg-borderSecondary {
  background: $borderSecondary !important;
}

.text-backgroundSecondary {
  color: $backgroundSecondary !important;
}

.bg-backgroundSecondary {
  background: $backgroundSecondary !important;
}

.text-neutralHighest {
  color: $neutralHighest !important;
}

.bg-neutralHighest {
  background: $neutralHighest !important;
}

.text-neutralHigher {
  color: $neutralHigher !important;
}

.bg-neutralHigher {
  color: $neutralHigher !important;
}

.text-neutralHigh {
  color: $neutralHigh !important;
}

.bg-neutralHigh {
  background: $neutralHigh !important;
}

.text-neutralLowest {
  color: $neutralLowest !important;
}

.bg-neutralLowest {
  background: $neutralLowest !important;
}

.bg-backgroundSecondary {
  background: $backgroundSecondary !important;
}


.bg-infoLowest {
  background: $infoLowest !important;
}

.text-infoMedium {
  color: $infoMedium !important;
}
// Custom quasar dark mode classes

.body--dark {
  .bg-backgroundSecondary {
    background: $backgroundSecondaryDark !important;
  }

  .text-neutralHighest {
    color: $neutralHighestDark !important;
  }

  .bg-neutralHighest {
    background: $neutralHighestDark !important;
  }

  .text-neutralHigher {
    color: $neutralHigherDark !important;
  }

  .bg-neutralHigher {
    background: $neutralHigherDark !important;
  }

  .text-borderSecondary {
    color: $borderSecondaryDark !important;
  }

  .bg-borderSecondary {
    background: $borderSecondaryDark !important;
  }

  .text-neutralLowest {
    color: $neutralLowestDark !important;
  }

  .bg-neutralLowest {
    background: $neutralLowestDark !important;
  }

  .bg-backgroundSecondary {
    background: $backgroundSecondaryDark !important;
  }
}
